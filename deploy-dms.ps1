# Samsung DMS Deployment Script
# Run as Administrator

param(
    [Parameter(Mandatory=$true)]
    [string]$Domain,
    
    [Parameter(Mandatory=$true)]
    [string]$CloudflareToken,
    
    [string]$DBPassword = "SecurePassword123!",
    [string]$InstallPath = "C:\DMS"
)

Write-Host "=== Samsung DMS Deployment Script ===" -ForegroundColor Green

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "This script must be run as Administrator!"
    exit 1
}

# Create installation directory
Write-Host "Creating installation directory..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path $InstallPath
New-Item -ItemType Directory -Force -Path "$InstallPath\logs"
New-Item -ItemType Directory -Force -Path "$InstallPath\config"

# Install Chocolatey if not present
if (!(Get-Command choco -ErrorAction SilentlyContinue)) {
    Write-Host "Installing Chocolatey..." -ForegroundColor Yellow
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    refreshenv
}

# Install required software
Write-Host "Installing required software..." -ForegroundColor Yellow
choco install -y postgresql --params "/Password:$DBPassword"
choco install -y cloudflared
choco install -y nssm
choco install -y pgadmin4

# Wait for PostgreSQL to start
Write-Host "Waiting for PostgreSQL to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Create DMS database
Write-Host "Setting up database..." -ForegroundColor Yellow
$env:PGPASSWORD = $DBPassword
& "C:\Program Files\PostgreSQL\15\bin\psql.exe" -U postgres -c "CREATE DATABASE samsung_dms;"
& "C:\Program Files\PostgreSQL\15\bin\psql.exe" -U postgres -c "CREATE USER dms_user WITH PASSWORD 'dms_password';"
& "C:\Program Files\PostgreSQL\15\bin\psql.exe" -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE samsung_dms TO dms_user;"

# Copy Samsung DMS executable
Write-Host "Setting up Samsung DMS application..." -ForegroundColor Yellow
if (Test-Path "Samsung_Whiteboard_DMS_23.1000.16.exe") {
    Copy-Item "Samsung_Whiteboard_DMS_23.1000.16.exe" "$InstallPath\"
} else {
    Write-Warning "Samsung DMS executable not found in current directory!"
    Write-Host "Please copy Samsung_Whiteboard_DMS_23.1000.16.exe to $InstallPath manually"
}

# Setup Cloudflare Tunnel
Write-Host "Setting up Cloudflare Tunnel..." -ForegroundColor Yellow

# Login to Cloudflare (this will open browser)
Write-Host "Please complete Cloudflare login in the browser window that opens..."
cloudflared tunnel login

# Create tunnel
$tunnelName = "samsung-dms-$(Get-Random -Maximum 9999)"
cloudflared tunnel create $tunnelName

# Get tunnel ID
$tunnelInfo = cloudflared tunnel list --output json | ConvertFrom-Json
$tunnelId = ($tunnelInfo | Where-Object { $_.name -eq $tunnelName }).id

Write-Host "Created tunnel: $tunnelName (ID: $tunnelId)" -ForegroundColor Green

# Create tunnel configuration
$configContent = @"
tunnel: $tunnelId
credentials-file: C:\Users\<USER>\.cloudflared\$tunnelId.json

ingress:
  - hostname: dms.$Domain
    service: http://localhost:8080
    originRequest:
      connectTimeout: 30s
      tlsTimeout: 30s
      tcpKeepAlive: 30s
      httpHostHeader: dms.$Domain

  - hostname: api.$Domain
    service: http://localhost:8080
    path: /api/*

  - hostname: enroll.$Domain
    service: http://localhost:8081

  - service: http_status:404
"@

$configPath = "C:\Users\<USER>\.cloudflared\config.yml"
$configContent | Out-File -FilePath $configPath -Encoding UTF8

Write-Host "Tunnel configuration saved to: $configPath" -ForegroundColor Green

# Create DNS records
Write-Host "Creating DNS records..." -ForegroundColor Yellow
$headers = @{
    "Authorization" = "Bearer $CloudflareToken"
    "Content-Type" = "application/json"
}

# Get zone ID
$zones = Invoke-RestMethod -Uri "https://api.cloudflare.com/client/v4/zones" -Headers $headers
$zoneId = ($zones.result | Where-Object { $_.name -eq $Domain }).id

if ($zoneId) {
    # Create CNAME records
    $dnsRecords = @(
        @{ name = "dms"; content = "$tunnelId.cfargotunnel.com" },
        @{ name = "api"; content = "$tunnelId.cfargotunnel.com" },
        @{ name = "enroll"; content = "$tunnelId.cfargotunnel.com" }
    )

    foreach ($record in $dnsRecords) {
        $body = @{
            type = "CNAME"
            name = $record.name
            content = $record.content
            proxied = $true
        } | ConvertTo-Json

        try {
            Invoke-RestMethod -Uri "https://api.cloudflare.com/client/v4/zones/$zoneId/dns_records" -Method POST -Headers $headers -Body $body
            Write-Host "Created DNS record: $($record.name).$Domain" -ForegroundColor Green
        } catch {
            Write-Warning "Failed to create DNS record for $($record.name): $($_.Exception.Message)"
        }
    }
} else {
    Write-Warning "Could not find zone ID for domain: $Domain"
    Write-Host "Please manually create CNAME records in Cloudflare dashboard:"
    Write-Host "  dms.$Domain -> $tunnelId.cfargotunnel.com"
    Write-Host "  api.$Domain -> $tunnelId.cfargotunnel.com"
    Write-Host "  enroll.$Domain -> $tunnelId.cfargotunnel.com"
}

# Install tunnel as Windows service
Write-Host "Installing Cloudflare Tunnel as Windows service..." -ForegroundColor Yellow
cloudflared service install

# Start the tunnel service
Start-Service cloudflared

# Create DMS service wrapper
Write-Host "Setting up Samsung DMS service..." -ForegroundColor Yellow
$dmsServiceScript = @"
@echo off
cd /d "$InstallPath"
Samsung_Whiteboard_DMS_23.1000.16.exe
"@

$dmsServiceScript | Out-File -FilePath "$InstallPath\start-dms.bat" -Encoding ASCII

# Install DMS as Windows service using NSSM
nssm install "Samsung DMS" "$InstallPath\start-dms.bat"
nssm set "Samsung DMS" DisplayName "Samsung Whiteboard DMS"
nssm set "Samsung DMS" Description "Samsung Whiteboard Device Management System"
nssm set "Samsung DMS" Start SERVICE_AUTO_START

# Configure Windows Firewall
Write-Host "Configuring Windows Firewall..." -ForegroundColor Yellow
New-NetFirewallRule -DisplayName "Samsung DMS HTTP" -Direction Inbound -Protocol TCP -LocalPort 8080 -Action Allow
New-NetFirewallRule -DisplayName "Samsung DMS Enrollment" -Direction Inbound -Protocol TCP -LocalPort 8081 -Action Allow
New-NetFirewallRule -DisplayName "PostgreSQL" -Direction Inbound -Protocol TCP -LocalPort 5432 -Action Allow

Write-Host "=== Deployment Complete! ===" -ForegroundColor Green
Write-Host ""
Write-Host "Your Samsung DMS is now accessible at:" -ForegroundColor Cyan
Write-Host "  Main Interface: https://dms.$Domain" -ForegroundColor White
Write-Host "  API Endpoint: https://api.$Domain" -ForegroundColor White
Write-Host "  Device Enrollment: https://enroll.$Domain" -ForegroundColor White
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Start the Samsung DMS service: Start-Service 'Samsung DMS'"
Write-Host "2. Check tunnel status: cloudflared tunnel info $tunnelName"
Write-Host "3. View logs: Get-Content '$InstallPath\logs\dms.log' -Wait"
Write-Host "4. Configure your Samsung devices to connect to: https://enroll.$Domain"
Write-Host ""
Write-Host "Database connection details:" -ForegroundColor Yellow
Write-Host "  Host: localhost"
Write-Host "  Port: 5432"
Write-Host "  Database: samsung_dms"
Write-Host "  Username: dms_user"
Write-Host "  Password: dms_password"
