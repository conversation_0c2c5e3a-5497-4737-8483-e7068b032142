# Quick Cloudflare Tunnel Setup for Samsung DMS
# Run as Administrator

param(
    [Parameter(Mandatory=$true)]
    [string]$Domain,
    
    [string]$Subdomain = "dms"
)

Write-Host "=== Cloudflare Tunnel Setup for Samsung DMS ===" -ForegroundColor Green
Write-Host "Domain: $Domain" -ForegroundColor Cyan
Write-Host "DMS URL will be: https://$Subdomain.$Domain" -ForegroundColor Cyan
Write-Host ""

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "This script must be run as Administrator!"
    exit 1
}

# Check if Samsung DMS is running
$dmsService = Get-Service -Name "SamsungWhiteboardDMS" -ErrorAction SilentlyContinue
if (!$dmsService) {
    Write-Error "Samsung DMS service not found. Please install Samsung DMS first."
    exit 1
}

if ($dmsService.Status -ne "Running") {
    Write-Host "Starting Samsung DMS service..." -ForegroundColor Yellow
    Start-Service "SamsungWhiteboardDMS"
    Start-Sleep -Seconds 10
}

# Test local DMS connectivity
Write-Host "Testing Samsung DMS connectivity..." -ForegroundColor Yellow
try {
    Invoke-WebRequest -Uri "https://localhost:7010/aidms" -SkipCertificateCheck -TimeoutSec 10 -ErrorAction Stop | Out-Null
    Write-Host "✓ Samsung DMS is accessible on port 7010" -ForegroundColor Green
} catch {
    Write-Warning "Cannot connect to Samsung DMS on port 7010."
    Write-Host "Please check if the service is running and try again."
    exit 1
}

# Install Chocolatey if needed
if (!(Get-Command choco -ErrorAction SilentlyContinue)) {
    Write-Host "Installing Chocolatey..." -ForegroundColor Yellow
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
}

# Install Cloudflare Tunnel
Write-Host "Installing Cloudflare Tunnel..." -ForegroundColor Yellow
choco install -y cloudflared

# Refresh environment variables
$env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")

Write-Host ""
Write-Host "=== Manual Steps Required ===" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. Login to Cloudflare (this will open your browser):"
Write-Host "   cloudflared tunnel login" -ForegroundColor Cyan
Write-Host ""
Write-Host "2. Create a tunnel:"
Write-Host "   cloudflared tunnel create samsung-dms" -ForegroundColor Cyan
Write-Host ""
Write-Host "3. Create the configuration file at:"
Write-Host "   C:\Users\<USER>\.cloudflared\config.yml" -ForegroundColor Cyan
Write-Host ""
Write-Host "   With this content:"
Write-Host @"
tunnel: [YOUR-TUNNEL-ID]
credentials-file: C:\Users\<USER>\.cloudflared\[YOUR-TUNNEL-ID].json

ingress:
  - hostname: $Subdomain.$Domain
    service: https://localhost:7010
    originRequest:
      noTLSVerify: true
      connectTimeout: 30s
      
  - hostname: api.$Domain
    service: https://localhost:7010
    path: /aidms/api/*
    originRequest:
      noTLSVerify: true
      
  - service: http_status:404
"@ -ForegroundColor White

Write-Host ""
Write-Host "4. Create DNS record in Cloudflare dashboard:"
Write-Host "   Type: CNAME" -ForegroundColor Cyan
Write-Host "   Name: $Subdomain" -ForegroundColor Cyan
Write-Host "   Content: [YOUR-TUNNEL-ID].cfargotunnel.com" -ForegroundColor Cyan
Write-Host "   Proxy: Enabled (orange cloud)" -ForegroundColor Cyan
Write-Host ""
Write-Host "5. Install and start the tunnel service:"
Write-Host "   cloudflared service install" -ForegroundColor Cyan
Write-Host "   Start-Service cloudflared" -ForegroundColor Cyan
Write-Host ""
Write-Host "6. Test your setup:"
Write-Host "   https://$Subdomain.$Domain/aidms" -ForegroundColor Cyan
Write-Host ""

# Configure Windows Firewall
Write-Host "Configuring Windows Firewall..." -ForegroundColor Yellow
try {
    New-NetFirewallRule -DisplayName "Samsung DMS HTTPS" -Direction Inbound -Protocol TCP -LocalPort 7010 -Action Allow -ErrorAction SilentlyContinue
    Write-Host "✓ Firewall rule added for port 7010" -ForegroundColor Green
} catch {
    Write-Warning "Could not add firewall rule. You may need to add it manually."
}

Write-Host ""
Write-Host "=== Setup Complete! ===" -ForegroundColor Green
Write-Host "Follow the manual steps above to complete the Cloudflare Tunnel setup." -ForegroundColor Yellow
Write-Host ""
Write-Host "Need help? Check the logs:" -ForegroundColor Yellow
Write-Host "- DMS logs: C:\Aidms\logs\aidms.log"
Write-Host "- Tunnel logs: C:\Users\<USER>\.cloudflared\cloudflared.log"
