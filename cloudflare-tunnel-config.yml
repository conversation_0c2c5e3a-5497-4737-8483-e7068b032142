# Cloudflare Tunnel Configuration
# Save as: C:\Users\<USER>\.cloudflared\config.yml

tunnel: samsung-dms
credentials-file: C:\Users\<USER>\.cloudflared\your-tunnel-id.json

ingress:
  # Main DMS application
  - hostname: dms.yourdomain.com
    service: http://localhost:8080
    originRequest:
      connectTimeout: 30s
      tlsTimeout: 30s
      tcpKeepAlive: 30s
      noHappyEyeballs: false
      keepAliveTimeout: 90s
      httpHostHeader: dms.yourdomain.com

  # Device enrollment endpoint (if different port)
  - hostname: enroll.yourdomain.com
    service: http://localhost:8081
    originRequest:
      connectTimeout: 30s

  # Database admin (optional - be careful with security)
  - hostname: db-admin.yourdomain.com
    service: http://localhost:5050
    originRequest:
      connectTimeout: 30s

  # API subdomain
  - hostname: api.yourdomain.com
    service: http://localhost:8080
    path: /api/*

  # Catch-all rule (must be last)
  - service: http_status:404
