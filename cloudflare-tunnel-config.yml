# Cloudflare Tunnel Configuration for Samsung DMS
# Save as: C:\Users\<USER>\.cloudflared\config.yml

tunnel: samsung-dms
credentials-file: C:\Users\<USER>\.cloudflared\your-tunnel-id.json

ingress:
  # Main Samsung DMS application (HTTPS on port 7010)
  - hostname: dms.yourdomain.com
    service: https://localhost:7010
    originRequest:
      connectTimeout: 30s
      tlsTimeout: 30s
      tcpKeepAlive: 30s
      noHappyEyeballs: false
      keepAliveTimeout: 90s
      httpHostHeader: dms.yourdomain.com
      # Skip certificate verification for self-signed cert
      noTLSVerify: true

  # API endpoints (same service, different path)
  - hostname: api.yourdomain.com
    service: https://localhost:7010
    path: /aidms/api/*
    originRequest:
      connectTimeout: 30s
      noTLSVerify: true

  # Device enrollment endpoint
  - hostname: enroll.yourdomain.com
    service: https://localhost:7010
    path: /aidms/enroll/*
    originRequest:
      connectTimeout: 30s
      noTLSVerify: true

  # Admin interface
  - hostname: admin.yourdomain.com
    service: https://localhost:7010
    path: /aidms/admin/*
    originRequest:
      connectTimeout: 30s
      noTLSVerify: true

  # Catch-all rule (must be last)
  - service: http_status:404
