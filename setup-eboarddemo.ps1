# Samsung DMS Setup for eboarddemo.com
# Run as Administrator

Write-Host "=== Samsung DMS Setup for eboarddemo.com ===" -ForegroundColor Green
Write-Host ""

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsIn<PERSON><PERSON>([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "This script must be run as Administrator!"
    exit 1
}

# Check Samsung DMS
$dmsService = Get-Service -Name "SamsungWhiteboardDMS" -ErrorAction SilentlyContinue
if (!$dmsService -or $dmsService.Status -ne "Running") {
    Write-Error "Samsung DMS service not running. Please start it first."
    exit 1
}

Write-Host "✓ Samsung DMS is running" -ForegroundColor Green

# Test local connectivity
try {
    $null = Invoke-WebRequest -Uri "https://localhost:7010/aidms" -SkipCertificateCheck -TimeoutSec 5 -ErrorAction Stop
    Write-Host "✓ Samsung DMS accessible at https://localhost:7010/aidms" -ForegroundColor Green
}
catch {
    Write-Error "Cannot connect to Samsung DMS. Please check the service."
    exit 1
}

# Install Cloudflare Tunnel if needed
if (!(Get-Command cloudflared -ErrorAction SilentlyContinue)) {
    Write-Host "Installing Cloudflare Tunnel..." -ForegroundColor Yellow
    
    if (!(Get-Command choco -ErrorAction SilentlyContinue)) {
        Write-Host "Installing Chocolatey first..." -ForegroundColor Yellow
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
    }
    
    choco install -y cloudflared
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
}

Write-Host "✓ Cloudflare Tunnel installed" -ForegroundColor Green

# Configure Windows Firewall
Write-Host "Configuring Windows Firewall..." -ForegroundColor Yellow
try {
    New-NetFirewallRule -DisplayName "Samsung DMS HTTPS" -Direction Inbound -Protocol TCP -LocalPort 7010 -Action Allow -ErrorAction SilentlyContinue
    Write-Host "✓ Firewall configured for port 7010" -ForegroundColor Green
}
catch {
    Write-Warning "Could not configure firewall automatically"
}

Write-Host ""
Write-Host "=== Next Steps ===" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. Login to Cloudflare (opens browser):"
Write-Host "   cloudflared tunnel login" -ForegroundColor Cyan
Write-Host ""
Write-Host "2. Create tunnel:"
Write-Host "   cloudflared tunnel create samsung-dms" -ForegroundColor Cyan
Write-Host ""
Write-Host "3. Note the tunnel ID from the output, then create config file:"
Write-Host "   C:\Users\<USER>\.cloudflared\config.yml" -ForegroundColor Cyan
Write-Host ""

$configContent = @"
tunnel: [REPLACE-WITH-YOUR-TUNNEL-ID]
credentials-file: C:\Users\<USER>\.cloudflared\[REPLACE-WITH-YOUR-TUNNEL-ID].json

ingress:
  - hostname: dms.eboarddemo.com
    service: https://localhost:7010
    originRequest:
      noTLSVerify: true
      connectTimeout: 30s
      
  - hostname: api.eboarddemo.com
    service: https://localhost:7010
    path: /aidms/api/*
    originRequest:
      noTLSVerify: true
      
  - service: http_status:404
"@

Write-Host "   Config file content:" -ForegroundColor White
Write-Host $configContent -ForegroundColor Gray
Write-Host ""

Write-Host "4. Create DNS records in Cloudflare Dashboard:"
Write-Host "   • Go to: https://dash.cloudflare.com" -ForegroundColor Cyan
Write-Host "   • Select: eboarddemo.com" -ForegroundColor Cyan
Write-Host "   • Go to: DNS tab" -ForegroundColor Cyan
Write-Host "   • Add CNAME record:" -ForegroundColor Cyan
Write-Host "     Name: dms" -ForegroundColor White
Write-Host "     Content: [YOUR-TUNNEL-ID].cfargotunnel.com" -ForegroundColor White
Write-Host "     Proxy: ON (orange cloud)" -ForegroundColor White
Write-Host "   • Add CNAME record:" -ForegroundColor Cyan
Write-Host "     Name: api" -ForegroundColor White
Write-Host "     Content: [YOUR-TUNNEL-ID].cfargotunnel.com" -ForegroundColor White
Write-Host "     Proxy: ON (orange cloud)" -ForegroundColor White
Write-Host ""

Write-Host "5. Install tunnel as Windows service:"
Write-Host "   cloudflared service install" -ForegroundColor Cyan
Write-Host ""

Write-Host "6. Start the tunnel:"
Write-Host "   Start-Service cloudflared" -ForegroundColor Cyan
Write-Host ""

Write-Host "7. Test your setup:"
Write-Host "   https://dms.eboarddemo.com/aidms" -ForegroundColor Cyan
Write-Host "   https://api.eboarddemo.com/aidms/api/" -ForegroundColor Cyan
Write-Host ""

Write-Host "=== Your Samsung devices can now connect to ===" -ForegroundColor Green
Write-Host "https://dms.eboarddemo.com/aidms" -ForegroundColor White
Write-Host ""

Write-Host "Troubleshooting:" -ForegroundColor Yellow
Write-Host "• DMS logs: C:\Aidms\logs\aidms.log"
Write-Host "• Tunnel logs: C:\Users\<USER>\.cloudflared\cloudflared.log"
Write-Host "• Check services: Get-Service SamsungWhiteboardDMS, cloudflared"
Write-Host "• Test local first: https://localhost:7010/aidms"
