# Samsung DMS Cloud Infrastructure
terraform {
  required_providers {
    digitalocean = {
      source  = "digitalocean/digitalocean"
      version = "~> 2.0"
    }
  }
}

# Configure the DigitalOcean Provider
provider "digitalocean" {
  token = var.do_token
}

# Variables
variable "do_token" {
  description = "DigitalOcean API Token"
  type        = string
  sensitive   = true
}

variable "ssh_key_name" {
  description = "Name of SSH key in DigitalOcean"
  type        = string
  default     = "dms-key"
}

variable "region" {
  description = "DigitalOcean region"
  type        = string
  default     = "nyc1"
}

variable "domain_name" {
  description = "Domain name for DMS"
  type        = string
  default     = ""
}

# Create VPC
resource "digitalocean_vpc" "dms_vpc" {
  name     = "dms-vpc"
  region   = var.region
  ip_range = "*********/16"
}

# Create Firewall
resource "digitalocean_firewall" "dms_firewall" {
  name = "dms-firewall"

  droplet_ids = [digitalocean_droplet.dms_server.id]

  # SSH access
  inbound_rule {
    protocol         = "tcp"
    port_range       = "22"
    source_addresses = ["0.0.0.0/0", "::/0"]
  }

  # RDP access
  inbound_rule {
    protocol         = "tcp"
    port_range       = "3389"
    source_addresses = ["0.0.0.0/0", "::/0"]
  }

  # HTTP/HTTPS
  inbound_rule {
    protocol         = "tcp"
    port_range       = "80"
    source_addresses = ["0.0.0.0/0", "::/0"]
  }

  inbound_rule {
    protocol         = "tcp"
    port_range       = "443"
    source_addresses = ["0.0.0.0/0", "::/0"]
  }

  # PostgreSQL (restrict to VPC)
  inbound_rule {
    protocol         = "tcp"
    port_range       = "5432"
    source_addresses = ["*********/16"]
  }

  # Samsung DMS ports (adjust as needed)
  inbound_rule {
    protocol         = "tcp"
    port_range       = "8080-8090"
    source_addresses = ["0.0.0.0/0", "::/0"]
  }

  outbound_rule {
    protocol              = "tcp"
    port_range            = "1-65535"
    destination_addresses = ["0.0.0.0/0", "::/0"]
  }

  outbound_rule {
    protocol              = "udp"
    port_range            = "1-65535"
    destination_addresses = ["0.0.0.0/0", "::/0"]
  }
}

# Create Windows Server Droplet
resource "digitalocean_droplet" "dms_server" {
  image    = "windows-2022"
  name     = "dms-server"
  region   = var.region
  size     = "s-2vcpu-4gb"
  vpc_uuid = digitalocean_vpc.dms_vpc.id

  # User data script for initial setup
  user_data = file("${path.module}/scripts/windows-setup.ps1")

  tags = ["dms", "windows", "production"]
}

# Create PostgreSQL Database Droplet
resource "digitalocean_droplet" "postgres_server" {
  image    = "ubuntu-22-04-x64"
  name     = "postgres-server"
  region   = var.region
  size     = "s-1vcpu-2gb"
  vpc_uuid = digitalocean_vpc.dms_vpc.id

  # User data script for PostgreSQL setup
  user_data = file("${path.module}/scripts/postgres-setup.sh")

  tags = ["dms", "database", "production"]
}

# Create Load Balancer
resource "digitalocean_loadbalancer" "dms_lb" {
  name   = "dms-loadbalancer"
  region = var.region
  vpc_uuid = digitalocean_vpc.dms_vpc.id

  forwarding_rule {
    entry_protocol  = "http"
    entry_port      = 80
    target_protocol = "http"
    target_port     = 80
  }

  forwarding_rule {
    entry_protocol  = "https"
    entry_port      = 443
    target_protocol = "http"
    target_port     = 80
    tls_passthrough = false
  }

  healthcheck {
    protocol = "http"
    port     = 80
    path     = "/health"
  }

  droplet_ids = [digitalocean_droplet.dms_server.id]
}

# Domain configuration (optional)
resource "digitalocean_domain" "dms_domain" {
  count = var.domain_name != "" ? 1 : 0
  name  = var.domain_name
}

resource "digitalocean_record" "dms_a_record" {
  count  = var.domain_name != "" ? 1 : 0
  domain = digitalocean_domain.dms_domain[0].name
  type   = "A"
  name   = "@"
  value  = digitalocean_loadbalancer.dms_lb.ip
}

# Outputs
output "windows_server_ip" {
  value = digitalocean_droplet.dms_server.ipv4_address
  description = "Public IP of Windows DMS server"
}

output "postgres_server_ip" {
  value = digitalocean_droplet.postgres_server.ipv4_address_private
  description = "Private IP of PostgreSQL server"
}

output "load_balancer_ip" {
  value = digitalocean_loadbalancer.dms_lb.ip
  description = "Load balancer public IP"
}

output "domain_url" {
  value = var.domain_name != "" ? "https://${var.domain_name}" : "http://${digitalocean_loadbalancer.dms_lb.ip}"
  description = "URL to access DMS"
}
